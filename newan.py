import os
import re
import time
from collections import Counter, defaultdict

def numerical_sort(value):
    """
    按照文件名中的数字部分进行排序的函数。
    """
    numbers = re.findall(r'\d+', value)
    return list(map(int, numbers))

def analyze_flash_patterns_v5_multi_target(label_folder, num_frames, start_from_frame=25, threshold=0.2):
    """
    分析从指定帧开始的每个TrackID对象的闪烁模式，基于非0类与0类的比例关系生成闪烁模式。
    支持多目标情况，按TrackID分组分析。
    """
    files = sorted(os.listdir(label_folder), key=numerical_sort)
    
    # 按TrackID分组存储数据
    track_data = defaultdict(lambda: defaultdict(list))  # track_id -> frame -> [(class_id, confidence, x, y, w, h), ...]

    # 读取文件并提取每帧的每个TrackID的class_id
    for file in files:
        frame_number = int(file.split("_")[-1].split(".")[0])
        if frame_number < start_from_frame:
            continue

        with open(os.path.join(label_folder, file), 'r') as f:
            lines = f.readlines()
            for line in lines:
                data = line.strip().split()
                if len(data) >= 7:  # track_id, class_id, conf, x, y, w, h
                    track_id = int(data[0])
                    class_id = int(data[1])
                    confidence = float(data[2])
                    x, y, w, h = map(float, data[3:7])
                    
                    track_data[track_id][frame_number].append({
                        'class_id': class_id,
                        'confidence': confidence,
                        'x': x, 'y': y, 'w': w, 'h': h
                    })

    # 为每个TrackID分析闪烁模式
    all_track_patterns = {}
    
    for track_id, frame_data in track_data.items():
        flash_patterns = []
        
        # 获取该TrackID的所有帧范围
        all_frames = sorted(frame_data.keys())
        if len(all_frames) < num_frames:
            continue  # 跳过帧数不足的TrackID
            
        for start_frame in range(min(all_frames), max(all_frames) - num_frames + 1):
            zero_count = 0
            non_zero_count = 0
            non_zero_class = None

            # 统计48帧中0类和非0类的数量
            for frame_number in range(start_frame, start_frame + num_frames):
                if frame_number in frame_data:
                    for detection in frame_data[frame_number]:
                        class_id = detection['class_id']
                        if class_id == 0:
                            zero_count += 1
                        else:
                            non_zero_class = class_id
                            non_zero_count += 1

            # 如果存在非0类，则计算比例并生成闪烁模式
            if non_zero_class is not None and zero_count > 0:
                ratio = non_zero_count / zero_count
                # 判断是否接近1:1 或 2:1
                if abs(ratio - 1) <= threshold:  # 接近1:1
                    flash_patterns.append(f"{non_zero_class}{non_zero_class}00")
                elif abs(ratio - 2) <= threshold:  # 接近2:1
                    flash_patterns.append(f"{non_zero_class}{non_zero_class}0")
                else:
                    flash_patterns.append("无闪烁")

        # 计算闪烁模式的众数
        if flash_patterns:
            pattern_counts = Counter(flash_patterns)
            most_common_patterns = pattern_counts.most_common(3)  # 获取最多的前三个模式
            final_patterns = [pattern for pattern, _ in most_common_patterns]
            all_track_patterns[track_id] = final_patterns
        else:
            all_track_patterns[track_id] = ["无闪烁"]  # 如果没有检测到闪烁模式，返回默认模式

    return all_track_patterns

def analyze_flash_patterns_v5(label_folder, num_frames, start_from_frame=25, threshold=0.2):
    """
    向后兼容的单一目标分析函数
    """
    all_track_patterns = analyze_flash_patterns_v5_multi_target(label_folder, num_frames, start_from_frame, threshold)
    
    # 返回第一个TrackID的结果（保持向后兼容）
    if all_track_patterns:
        first_track_id = min(all_track_patterns.keys())
        return all_track_patterns[first_track_id]
    else:
        return ["无闪烁"]

def map_patterns_to_numbers_multi_target(all_track_patterns):
    """
    将多个TrackID的闪烁模式映射到对应的数字。
    """
    pattern_mapping = {
        '220': 1,
        '330': 2,
        '110': 3,
        '550': 4,
        '440': 5,
        '2200': 6,
        '3300': 7,
        '1100': 8,
        '5500': 9,
        '4400': 10
    }
    
    track_numbers = {}
    for track_id, patterns in all_track_patterns.items():
        numbers = [pattern_mapping.get(pattern, -1) for pattern in patterns]
        track_numbers[track_id] = numbers
    
    return track_numbers

def map_patterns_to_numbers(patterns):
    """
    向后兼容的单一目标映射函数
    """
    pattern_mapping = {
        '220': 1,
        '330': 2,
        '110': 3,
        '550': 4,
        '440': 5,
        '2200': 6,
        '3300': 7,
        '1100': 8,
        '5500': 9,
        '4400': 10
    }
    return [pattern_mapping.get(pattern, -1) for pattern in patterns]  # -1表示模式未找到





# # 主逻辑
# label_folder ='/home/<USER>/nnDataset/0920/labels'
# num_frames = 48  # 连续帧数


# # 调用新的解析方法
# flash_patterns = analyze_flash_patterns_v5(label_folder, num_frames, start_from_frame=1, threshold=0.2)

# print('SSSSSSmapped_number',flash_patterns)

# # 映射模式到数字
# mapped_numbers = map_patterns_to_numbers(flash_patterns)


# # 输出闪烁模式及其对应数字
# print("Detected flash patterns (most common) and their mapped numbers:")
# for pattern, number in zip(flash_patterns, mapped_numbers):
#     if pattern != "无闪烁":
#         print(f"Pattern: {pattern}, Mapped Number: {number}")
