#!/usr/bin/env python3
"""
数据格式测试脚本
验证标签文件的坐标格式
"""

import os
import numpy as np

def test_data_format():
    """测试数据格式"""
    print("📋 数据格式测试")
    print("=" * 40)
    
    # 测试路径
    base_path = '/home/<USER>/nnDataset/0920/labels'
    
    # 查找可用的摄像头文件夹
    for cam_name in ['cam0', 'cam1', 'cam2', 'cam3']:
        label_path = os.path.join(base_path, cam_name)
        if os.path.exists(label_path):
            txt_files = [f for f in os.listdir(label_path) if f.endswith('.txt')]
            if txt_files:
                print(f"\n📹 摄像头: {cam_name}")
                print(f"   标签文件数量: {len(txt_files)}")
                
                # 分析前几个文件
                for i, txt_file in enumerate(sorted(txt_files)[:3]):
                    file_path = os.path.join(label_path, txt_file)
                    print(f"\n   文件 {i+1}: {txt_file}")
                    
                    with open(file_path, 'r') as f:
                        lines = f.readlines()
                        print(f"     行数: {len(lines)}")
                        
                        if lines:
                            # 分析第一行
                            first_line = lines[0].strip()
                            data = first_line.split()
                            print(f"     第一行: {first_line}")
                            print(f"     字段数: {len(data)}")
                            
                            if len(data) >= 7:
                                track_id = int(data[0])
                                class_id = int(data[1])
                                confidence = float(data[2])
                                x, y, w, h = map(float, data[3:7])
                                
                                print(f"     TrackID: {track_id}")
                                print(f"     ClassID: {class_id}")
                                print(f"     Confidence: {confidence:.4f}")
                                print(f"     X: {x:.2f}, Y: {y:.2f}, W: {w:.2f}, H: {h:.2f}")
                                
                                # 判断坐标格式
                                if x > 1.0 or y > 1.0 or w > 1.0 or h > 1.0:
                                    print(f"     ✅ 像素坐标格式 (X={x:.1f}, Y={y:.1f}, W={w:.1f}, H={h:.1f})")
                                else:
                                    print(f"     ✅ 归一化坐标格式 (X={x:.3f}, Y={y:.3f}, W={w:.3f}, H={h:.3f})")
                                
                                # 统计所有行的TrackID和ClassID
                                track_ids = set()
                                class_ids = set()
                                for line in lines:
                                    line_data = line.strip().split()
                                    if len(line_data) >= 2:
                                        track_ids.add(int(line_data[0]))
                                        class_ids.add(int(line_data[1]))
                                
                                print(f"     所有TrackID: {sorted(track_ids)}")
                                print(f"     所有ClassID: {sorted(class_ids)}")
                            else:
                                print(f"     ❌ 格式错误，期望至少7个字段")
                
                break  # 只测试第一个找到的摄像头
    else:
        print("❌ 未找到可用的标签文件")

def test_coordinate_conversion():
    """测试坐标转换"""
    print("\n🔄 坐标转换测试")
    print("=" * 30)
    
    # 测试像素坐标
    pixel_coords = [320.36, 188.41, 39.80, 24.51]
    print(f"像素坐标: {pixel_coords}")
    
    # 转换为归一化坐标
    image_width, image_height = 640, 360
    normalized_coords = [
        pixel_coords[0] / image_width,   # x
        pixel_coords[1] / image_height,  # y
        pixel_coords[2] / image_width,   # w
        pixel_coords[3] / image_height   # h
    ]
    print(f"归一化坐标: {[f'{x:.4f}' for x in normalized_coords]}")
    
    # 转换回像素坐标
    back_to_pixel = [
        normalized_coords[0] * image_width,
        normalized_coords[1] * image_height,
        normalized_coords[2] * image_width,
        normalized_coords[3] * image_height
    ]
    print(f"转换回像素: {[f'{x:.2f}' for x in back_to_pixel]}")
    
    # 验证转换是否正确
    diff = [abs(a - b) for a, b in zip(pixel_coords, back_to_pixel)]
    print(f"转换误差: {[f'{x:.6f}' for x in diff]}")

if __name__ == '__main__':
    test_data_format()
    test_coordinate_conversion() 